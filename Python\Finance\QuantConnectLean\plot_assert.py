import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
import pandas as pd
import re
import os
from datetime import datetime

# Define the log file path
log_file = r"D:\work\xstarwalker168\Python\Finance\QuantConnectLean\QC-Log-Dir\NebulaVilla-log.txt"

# Lists to store extracted data
bar_indices = []
fdusd_values = []
ohlc_data = []
signals = []
trade_events = []

# Regular expressions
fdusd_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) barIdx=(\d+), opening=0, position=0, pendingOrders=0, FDUSD=(\d+\.\d{2})"
vbt_pattern = r"(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) barIdx=(\d+), .*, vbt: `(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}), ([\d.]+), ([\d.]+), ([\d.]+), ([\d.]+), (\d+), ([012])`"
trade_pattern = r"barIdx=(\d+), .*, TimeDiff\(Filled\)=([\d.]+)s, QTY=([+-]?\d+), .*, fillPrice=([\d.]+), property=(\w+)"

# Read and process the log file
with open(log_file, 'r') as file:
    for line in file:
        # Extract FDUSD data
        fdusd_match = re.search(fdusd_pattern, line.strip())
        if fdusd_match:
            bar_idx = int(fdusd_match.group(2))
            fdusd = float(fdusd_match.group(3))
            bar_indices.append(bar_idx)
            fdusd_values.append(fdusd)
        
        # Extract OHLC and signal data from vbt lines
        vbt_match = re.search(vbt_pattern, line.strip())
        if vbt_match:
            timestamp = datetime.strptime(vbt_match.group(1), "%Y-%m-%d %H:%M:%S")
            bar_idx = int(vbt_match.group(2))
            vbt_timestamp = datetime.strptime(vbt_match.group(3), "%Y-%m-%d %H:%M:%S")
            open_price = float(vbt_match.group(4))
            high_price = float(vbt_match.group(5))
            low_price = float(vbt_match.group(6))
            close_price = float(vbt_match.group(7))
            volume = int(vbt_match.group(8))
            signal = int(vbt_match.group(9))
            
            ohlc_data.append({
                'timestamp': timestamp,
                'bar_idx': bar_idx,
                'vbt_timestamp': vbt_timestamp,
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume,
                'signal': signal
            })
        
        # Extract trade execution events
        trade_match = re.search(trade_pattern, line.strip())
        if trade_match:
            bar_idx = int(trade_match.group(1))
            time_diff = float(trade_match.group(2))
            qty = int(trade_match.group(3))
            fill_price = float(trade_match.group(4))
            property_type = trade_match.group(5)
            
            trade_events.append({
                'bar_idx': bar_idx,
                'time_diff': time_diff,
                'qty': qty,
                'fill_price': fill_price,
                'property': property_type,
                'is_buy': qty > 0,
                'is_open': 'open' in property_type.lower()
            })

# Convert to DataFrames for easier handling
if ohlc_data:
    ohlc_df = pd.DataFrame(ohlc_data)
    
if trade_events:
    trades_df = pd.DataFrame(trade_events)

# Create comprehensive plot
fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), sharex=True)

# Plot 1: OHLC Candlestick Chart
if ohlc_data:
    for i, row in ohlc_df.iterrows():
        bar_idx = row['bar_idx']
        open_price = row['open']
        high_price = row['high']
        low_price = row['low']
        close_price = row['close']
        
        # Determine color (green for up, red for down)
        color = 'green' if close_price >= open_price else 'red'
        
        # Draw the high-low line
        ax1.plot([bar_idx, bar_idx], [low_price, high_price], color='black', linewidth=1)
        
        # Draw the open-close rectangle
        height = abs(close_price - open_price)
        bottom = min(open_price, close_price)
        rect = Rectangle((bar_idx - 0.3, bottom), 0.6, height, 
                        facecolor=color, alpha=0.7, edgecolor='black')
        ax1.add_patch(rect)
    
    # Add buy/sell signals
    for i, row in ohlc_df.iterrows():
        if row['signal'] == 1:  # Short signal
            ax1.scatter(row['bar_idx'], row['high'] * 1.01, color='red', marker='v', s=100, label='Short Signal' if i == 0 else "")
        elif row['signal'] == 2:  # Long signal
            ax1.scatter(row['bar_idx'], row['low'] * 0.99, color='green', marker='^', s=100, label='Long Signal' if i == 0 else "")

ax1.set_title('OHLC Candlestick Chart with Buy/Sell Signals')
ax1.set_ylabel('Price')
ax1.grid(True, alpha=0.3)
ax1.legend()

# Plot 2: Trade Events
if trade_events:
    buy_events = [event for event in trade_events if event['is_buy']]
    sell_events = [event for event in trade_events if not event['is_buy']]
    open_events = [event for event in trade_events if event['is_open']]
    
    if buy_events:
        buy_bar_indices = [event['bar_idx'] for event in buy_events]
        buy_prices = [event['fill_price'] for event in buy_events]
        ax2.scatter(buy_bar_indices, buy_prices, color='green', marker='^', s=80, label='Buy Orders', alpha=0.8)
    
    if sell_events:
        sell_bar_indices = [event['bar_idx'] for event in sell_events]
        sell_prices = [event['fill_price'] for event in sell_events]
        ax2.scatter(sell_bar_indices, sell_prices, color='red', marker='v', s=80, label='Sell Orders', alpha=0.8)
    
    if open_events:
        open_bar_indices = [event['bar_idx'] for event in open_events]
        open_prices = [event['fill_price'] for event in open_events]
        ax2.scatter(open_bar_indices, open_prices, color='blue', marker='o', s=60, label='Open Positions', alpha=0.8)

ax2.set_title('Trade Execution Events')
ax2.set_ylabel('Fill Price')
ax2.grid(True, alpha=0.3)
ax2.legend()

# Plot 3: FDUSD Values
if bar_indices:
    ax3.plot(bar_indices, fdusd_values, 'b-', label='FDUSD Value', linewidth=2)

ax3.set_title('FDUSD Value Changes Over Time')
ax3.set_xlabel('Bar Index')
ax3.set_ylabel('FDUSD')
ax3.grid(True, alpha=0.3)
ax3.legend()

# Set x-axis ticks every 500 barIdx for all subplots
if bar_indices or ohlc_data:
    all_bar_indices = bar_indices + [row['bar_idx'] for row in ohlc_data]
    if all_bar_indices:
        start = min(all_bar_indices)
        end = max(all_bar_indices)
        tick_range = range(start, end + 500, 500)
        for ax in [ax1, ax2, ax3]:
            ax.set_xticks(tick_range)

plt.tight_layout()

# Save plots
log_dir = os.path.dirname(log_file)
ohlc_output_path = os.path.join(log_dir, 'ohlc_trades_plot.png')
plt.savefig(ohlc_output_path, dpi=300, bbox_inches='tight')

plt.show()

print(f"Plots saved to:")
print(f"- OHLC and trades: {ohlc_output_path}")
if ohlc_data:
    print(f"\nProcessed {len(ohlc_data)} OHLC bars")
if trade_events:
    print(f"Processed {len(trade_events)} trade events")
    buy_count = len([e for e in trade_events if e['is_buy']])
    sell_count = len([e for e in trade_events if not e['is_buy']])
    open_count = len([e for e in trade_events if e['is_open']])
    print(f"- {buy_count} buy orders, {sell_count} sell orders, {open_count} open positions")
